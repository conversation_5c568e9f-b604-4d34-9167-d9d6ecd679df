import {CommonModule} from '@angular/common';
import {Component, computed, effect, Input, OnDestroy, OnInit, signal, untracked, ViewChild} from '@angular/core';
import {FormsModule} from '@angular/forms';
import {SelectorMap} from '@creactives/models';
import {Tam4TranslationModule, Tam4TranslationService} from '@creactives/tam4-translation-core';
import {Store} from '@ngrx/store';
import {TranslateModule, TranslateService} from '@ngx-translate/core';
import {ConfirmationService, MessageService} from 'primeng/api';
import {ButtonModule} from 'primeng/button';
import {CardModule} from 'primeng/card';
import {InputTextModule} from 'primeng/inputtext';
import {PanelModule} from 'primeng/panel';
import {Table, TableModule} from 'primeng/table';
import {TagModule} from 'primeng/tag';
import {ToastModule} from 'primeng/toast';
import {TooltipModule} from 'primeng/tooltip';
import {Tam4SelectorConfig, TamAbstractReduxComponent} from 'src/app/components';
import {DomainValuesOperationTypeTransformPipe} from '../common/status-transform.pipe';
import {ManagementValuesService} from '../management-values.service';
import {
  DomainValue,
  DomainValueAttributeColumn,
  DomainValueAttributeUpdate,
  DomainValues,
  DomainValuesOperationType,
  TreeDomainAttribute,
} from '../models/management-values.types';

const storeSelectors: Tam4SelectorConfig[] = [];

@Component({
  selector: 'div[attributeDomainValuesDetails]',
  template: `
    <div class="values-details-component">
      @if (valuesAttributes()) {
        <div>
          <p-table
              #dt1
              [value]="valuesAttributes()"
              dataKey="id"
              [editMode]="isEditable() ? 'cell' : null"
              [scrollable]="true"
              scrollHeight="500px"
              [frozenWidth]="'200px'"
              styleClass="w-100 p-datatable-compat p-datatable-striped mt-3 mb-3"
              [globalFilterFields]="globalFilterFields()"
              [tableStyle]="{ 'min-width': '50rem' }"
              sortField="id"
              [sortOrder]="1"
          >
            <ng-template pTemplate="caption">
              <div style="text-align: left">
                <span class="p-input-icon-left" style="width: 100%; position: relative;">
                  <i class="pi pi-search"></i>
                  <input #searchInput
                         pInputText
                         type="text"
                         [(ngModel)]="searchValue"
                         (input)="onSearchInputChange(searchInput.value, dt1)"
                         placeholder="{{ 'domainValuesManagement.domainValues.searchPlaceholder' | translate }}"
                         style="padding-left: 2.2rem;"/>
                </span>
              </div>
            </ng-template>
            <ng-template pTemplate="header">
              <tr>
                <th style="min-width: 200px; box-shadow: inset 0 0 0 #ccc;"
                    pFrozenColumn
                    pSortableColumn="id">
                  {{ 'domainValuesManagement.domainValues.columnValues' | translate }}
                  <p-sortIcon field="id"/>
                </th>
                @for (column of columnLanguages; track column.key; let i = $index) {
                  <th style="min-width:100px" [pSortableColumn]="getValueFieldName(i)">
                    {{ column.name }}
                    <p-sortIcon [field]="getValueFieldName(i)"/>
                  </th>
                }
                @if (isEditable()) {
                  <th style="width:15%">
                    {{ 'domainValuesManagement.domainValues.status' | translate }}
                  </th>
                  <th style="width:10%" alignFrozen="right" pFrozenColumn [frozen]="true" class="text-center">
                    {{ 'domainValuesManagement.domainValues.actions' | translate }}
                  </th>
                }
              </tr>
            </ng-template>

            <ng-template pTemplate="body" let-rowData>
              <tr [ngClass]="getRowClasses(rowData)">
                <td pFrozenColumn>{{ rowData.id }}</td>
                @for (lang of rowData.values; track lang.key) {
                  @if (isEditable()) {
                    <td [pEditableColumn]="canEditCell(rowData)">
                      <p-cellEditor>
                        <ng-template pTemplate="input">
                          <input
                              pInputText
                              type="text"
                              [(ngModel)]="lang.newValue"
                              (keydown.enter)="onCellValueChange(rowData, lang, $event)"
                              (keydown.escape)="onCellValueChange(rowData, lang, $event)"
                              (keydown.tab)="onCellValueChange(rowData, lang, $event)"
                              (blur)="onCellValueChange(rowData, lang, $event)"
                              [disabled]="!canEditCell(rowData)"
                          />
                        </ng-template>
                        <ng-template pTemplate="output">
                          <span [ngClass]="rowData.status === OperationTypes.UPDATE && lang.newValue !== lang.oldValue ? 'modified-value' : ''">
                            {{ lang.newValue }}
                          </span>
                        </ng-template>
                      </p-cellEditor>
                    </td>
                  } @else {
                    <td>
                      {{ lang.newValue }}
                    </td>
                  }
                }
                @if (isEditable()) {
                  <td>
                    @if (rowData.status?.trim()) {
                      <p-tag [value]="rowData.status"
                             [severity]="rowData.status | domainValuesOperationTypeTransform">
                      </p-tag>
                    }
                  </td>
                  <td alignFrozen="right" pFrozenColumn [frozen]="true" class="text-center">
                    @if (!isRowDeleted(rowData)) {
                      <button type="button"
                              (click)="onConfirmDelete(rowData)"
                              class="text-center p-button p-button-text p-0 border-0 bg-transparent"
                              pTooltip="{{ 'domainValuesManagement.domainValues.delete' | translate }}"
                              tooltipPosition="top">
                        <i class="pi pi-trash"></i>
                      </button>
                    } @else {
                      <button type="button"
                              (click)="onButtonRestore(rowData)"
                              class="text-center p-button p-button-text p-0 border-0 bg-transparent"
                              pTooltip="{{ 'domainValuesManagement.domainValues.restore' | translate }}"
                              tooltipPosition="top">
                        <i class="pi pi-undo"></i>
                      </button>
                    }
                  </td>
                }
              </tr>
            </ng-template>
            <ng-template pTemplate="footer" class="mb-3">
              @if (isEditable()) {
                <tr class="mb-3">
                  <td pFrozenColumn class="p-1">
                    <input
                        type="text"
                        [(ngModel)]="newRowData().id"
                        class="p-inputtext p-component"
                        placeholder="{{ 'domainValuesManagement.domainValues.columnValues' | translate }}"
                        [attr.aria-label]="'domainValuesManagement.domainValues.columnValues' | translate"
                        (keydown.enter)="onAddNewRow()"/>
                  </td>
                  @for (column of columnLanguages; track column.key; let i = $index) {
                    <td class="p-1">
                      <input
                          type="text"
                          class="p-inputtext p-component"
                          [ngModel]="getNewRowValue(i)"
                          (ngModelChange)="setNewRowValue(i, $event)"
                          placeholder="{{ column.key | countryTranslate }}"
                          [attr.aria-label]="column.key | countryTranslate"
                          (keydown.enter)="onAddNewRow()"/>
                    </td>
                  }
                  <td></td>
                  <td alignFrozen="right" pFrozenColumn [frozen]="true">
                    <div class="flex align-items-center justify-content-center gap-2">
                      <button
                          type="button"
                          (click)="onAddNewRow()"
                          class="p-button p-button-text p-0 border-0 bg-transparent save-button"
                          pTooltip="{{ 'domainValuesManagement.domainValues.save' | translate }}"
                          tooltipPosition="top">
                        <i class="pi pi-check"></i>
                      </button>
                      <button
                          type="button"
                          (click)="onCancelNewRow()"
                          class="p-button p-button-text p-0 border-0 bg-transparent cancel-button"
                          pTooltip="{{ 'domainValuesManagement.domainValues.cancel' | translate }}"
                          tooltipPosition="top">
                        <i class="pi pi-times"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              }
            </ng-template>
          </p-table>
        </div>
      }
    </div>
  `,
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    CardModule,
    InputTextModule,
    ButtonModule,
    TranslateModule,
    TagModule,
    ToastModule,
    TooltipModule,
    Tam4TranslationModule,
    PanelModule,
    DomainValuesOperationTypeTransformPipe
  ],
  styleUrls: ['./attribute-values-details.component.scss'],
  host: {
    '[class]': '\'values-details-component\''
  }
})
export class AttributeValuesDetailsComponent extends TamAbstractReduxComponent<SelectorMap> implements OnInit, OnDestroy {

  @Input() columnLanguages: DomainValueAttributeColumn[] = [];
  @Input() valuesAttributes = signal<DomainValues[]>([]);
  @Input() attribute = signal<TreeDomainAttribute | null>(null);

  @ViewChild('dt1') dt1!: Table;

  searchValue = signal<string>('');
  newRowData = signal<DomainValues>(this.createEmptyRow());

  globalFilterFields = computed(() => {
    const columns = this.columnLanguages;
    if (!columns?.length) {
      return ['id', 'status'];
    }

    return [
      'id',
      'status',
      ...columns.map((_, index) => `values.${index}.newValue`),
      ...columns.map((_, index) => `values.${index}.oldValue`)
    ];
  });


  isEditable = computed(() => {
    const attr = this.attribute();
    return Boolean(attr?.editable);
  });

  private attributeChangeEffect = effect(() => {
    const currentAttribute = this.attribute();
    if (currentAttribute) {
      untracked(() => {
        this.searchValue.set('');
        this.resetNewRow();
        setTimeout(() => {
          if (this.dt1) {
            this.onSearchInputChange('', this.dt1);
          }
        });
      });
    }
  }, { allowSignalWrites: true });

  protected readonly OperationTypes = DomainValuesOperationType;
  constructor(
      protected translate: TranslateService,
      protected tamTranslate: Tam4TranslationService,
      protected store: Store,
      private service: ManagementValuesService,
      private messageService: MessageService,
      private confirmationService: ConfirmationService
  ) {
    super(translate, tamTranslate, store, storeSelectors);
  }

  private isNewRowValid(): boolean {
    const newRow = this.newRowData();
    const rowId = newRow?.id?.trim();

    if (!rowId) {
      this.showError('domainValuesManagement.domainValues.errorValidatingAdd');
      return false;
    }

    const existingValues = this.valuesAttributes() || [];
    const isDuplicateId = existingValues.some(existingRow =>
        existingRow?.id === rowId && !this.isRowDeleted(existingRow)
    );

    if (isDuplicateId) {
      this.showError('domainValuesManagement.domainValues.errorValidatingKey');
      return false;
    }

    const hasValidBasicData = newRow?.values?.some(v => v?.newValue?.trim());
    if (!hasValidBasicData) {
      this.showError('domainValuesManagement.domainValues.errorValidatingAdd');
      return false;
    }

    return true;
  }

  ngOnInit() {
    super.ngOnInit();
    this.initializeComponent();
  }

  private initializeComponent(): void {
    if (this.attribute()) {
      this.resetNewRow();
    }
  }

  getValueFieldName(index: number): string {
    return `values.${index}.newValue`;
  }

  getRowClasses(rowData: DomainValues): Record<string, boolean> {
    return {
      'row-deleted': this.isRowDeleted(rowData),
      'row-added': this.isRowAdded(rowData)
    };
  }

  canEditCell(rowData: DomainValues): boolean {
    return this.isEditable() && rowData && !this.isRowDeleted(rowData);
  }

  getNewRowValue(index: number): string {
    return this.newRowData().values[index]?.newValue || '';
  }

  setNewRowValue(index: number, value: string): void {
    const currentRow = this.newRowData();
    const newValues = [...currentRow.values];
    newValues[index] = {...newValues[index], newValue: value};

    this.newRowData.set({
      ...currentRow,
      values: newValues
    });
  }

  onSearchInputChange(value: string, table?: Table): void {
    this.searchValue.set(value);
    if (table && typeof table.filterGlobal === 'function') {
      table.filterGlobal(value, 'contains');
    }
  }

  onCellValueChange(rowData: DomainValues, valueLang: DomainValue, event?: Event): void {
    event?.preventDefault();
    this.updateRowValue(rowData, valueLang);
  }

  private updateRowValue(rowData: DomainValues, valueLang: DomainValue): void {
    valueLang.edited = true;
    rowData.previousStatus = rowData.status;

    if (rowData.status !== DomainValuesOperationType.ADD) {
      rowData.status = DomainValuesOperationType.UPDATE;
    }

    rowData.editable = true;
    this.notifyValueUpdate(rowData);
  }

  onAddNewRow(): void {
    if (!this.isNewRowValid()) {
      return;
    }

    const newRow = this.createNewRowFromInput();
    this.addRowToTable(newRow);
    this.notifyValueUpdate(newRow);
    this.resetNewRow();
  }

  onCancelNewRow(): void {
    this.resetNewRow();
  }

  onConfirmDelete(rowDelete: DomainValues): void {
    this.confirmationService.confirm({
      header: this.translate.instant('domainValuesManagement.domainValues.deleteConfirmTitle'),
      message: this.translate.instant('domainValuesManagement.domainValues.deleteMessage'),
      acceptIcon: 'pi pi-check mr-2',
      rejectIcon: 'pi pi-times mr-2',
      acceptLabel: this.translate.instant('domainValuesManagement.summaryChanges.confirm'),
      rejectLabel: this.translate.instant('domainValuesManagement.button.buttonCancel'),
      rejectButtonStyleClass: 'p-button-sm',
      acceptButtonStyleClass: 'p-button-outlined p-button-sm',
      accept: () => {
        this.onDelete(rowDelete);
      }
    });
  }

  onDelete(rowDelete: DomainValues): void {
    if (!this.attribute()) { return; }

    if (rowDelete.status === DomainValuesOperationType.ADD) {
      this.removeRowCompletely(rowDelete);
      return;
    }

    this.service.action_doValidateDeleteAttributeValue(
        this.attribute().key,
        rowDelete.id,
        this.attribute().parentId,
        (isUsed: boolean) => this.handleDeleteValidation(rowDelete, isUsed),
        (error: any) => this.showError('domainValuesManagement.domainValues.errorValidatingDelete')
    );
  }

  onButtonRestore(rowRestore: DomainValues): void {
    rowRestore.status = (rowRestore.previousStatus === DomainValuesOperationType.ADD || rowRestore.previousStatus === DomainValuesOperationType.UPDATE  || rowRestore.previousStatus === DomainValuesOperationType.DELETE)
        ? rowRestore.previousStatus : null;
    rowRestore.previousStatus = DomainValuesOperationType.DELETE;
    this.notifyValueUpdate(rowRestore);
  }

  private createEmptyRow(): DomainValues {
    console.log(this.columnLanguages);
    return {
      id: '',
      status: null,
      values: this.columnLanguages?.map(column => ({
        key: column.value,
        oldValue: null,
        newValue: null,
        edited: false
      })) || [],
      editable: true
    };
  }

  private resetNewRow(): void {
    const columns = this.columnLanguages || [];
    this.newRowData.set({
      id: '',
      status: null,
      values: columns.map(column => ({
        key: column.value,
        oldValue: null,
        newValue: null,
        edited: false
      })),
      editable: true
    });
  }

  private createNewRowFromInput(): DomainValues {
    const inputRow = this.newRowData();
    inputRow.values.forEach(value => value.edited = true);

    return {
      id: inputRow.id,
      status: DomainValuesOperationType.ADD,
      values: [...inputRow.values],
      editable: true
    };
  }

  private addRowToTable(newRow: DomainValues): void {
    const currentValues = this.valuesAttributes() || [];
    const updatedValues = [...currentValues, newRow];
    this.valuesAttributes?.set(updatedValues);
  }

  private removeRowCompletely(rowToRemove: DomainValues): void {
    const currentValues = this.valuesAttributes() || [];
    const updatedValues = currentValues.filter(row => row.id !== rowToRemove.id);
    this.valuesAttributes?.set(updatedValues);
    this.notifyAddRowRemoval(rowToRemove);
  }

  private notifyAddRowRemoval(rowData: DomainValues): void {
    if (!this.attribute()) { return; }

    const removalUpdate: DomainValueAttributeUpdate = {
      attributeId: this.attribute().key,
      valueId: rowData.id,
      values: [],
      domainValues: [],
      status: DomainValuesOperationType.DELETE,
      previousStatus: DomainValuesOperationType.ADD
    };

    this.service.action_doUpdateDomainValues(removalUpdate);
  }

  private handleDeleteValidation(rowDelete: DomainValues, isUsed: boolean): void {
    if (isUsed) {
      this.showError('domainValuesManagement.domainValues.cannotDeleteValue');
      return;
    }

    rowDelete.previousStatus = rowDelete.status;
    rowDelete.status = DomainValuesOperationType.DELETE;
    this.notifyValueUpdate(rowDelete);
  }

  private notifyValueUpdate(rowData: DomainValues): void {
    if (!this.attribute()) { return; }

    const updatedValue: DomainValueAttributeUpdate = {
      attributeId: this.attribute().key,
      valueId: rowData.id,
      values: this.valuesAttributes().filter(val => val.id === rowData.id),
      domainValues: rowData.values.map(lang => ({
        key: lang.key,
        oldValue: lang.oldValue,
        newValue: lang.newValue,
        edited: lang.edited
      })),
      status: rowData.status,
      previousStatus: rowData.previousStatus
    };

    this.service.action_doUpdateDomainValues(updatedValue);
    if (rowData.status !== DomainValuesOperationType.UPDATE) {
      this.showSuccess('domainValuesManagement.domainValues.valueUpdated.' + (rowData.status ? rowData.status.toLowerCase() : 'restore'));
    }
  }

  private showMessage(severity: 'success' | 'error' | 'info' | 'warn', messageKey: string): void {
    const summaryKey = severity === 'success'
        ? 'domainValuesManagement.messages.success'
        : 'domainValuesManagement.messages.error';

    this.messageService.add({
      severity,
      summary: this.translate.instant(summaryKey),
      detail: this.translate.instant(messageKey)
    });
  }

  private showError(messageKey: string): void {
    this.showMessage('error', messageKey);
  }

  private showSuccess(messageKey: string): void {
    this.showMessage('success', messageKey);
  }

  isRowDeleted(rowData: DomainValues): boolean {
    return rowData.status === DomainValuesOperationType.DELETE;
  }

  isRowAdded(rowData: DomainValues): boolean {
    return rowData.status === DomainValuesOperationType.ADD;
  }

  ngOnDestroy() {
    super.ngOnDestroy();
  }
}
