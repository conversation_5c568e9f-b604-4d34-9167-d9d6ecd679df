import {CommonModule} from '@angular/common';
import {HttpClientModule} from '@angular/common/http';
import {ChangeDetectionStrategy, Component, effect, signal} from '@angular/core';
import {SelectorMap} from '@creactives/models';
import {Tam4TranslationService} from '@creactives/tam4-translation-core';
import {Store} from '@ngrx/store';
import {TranslateModule, TranslateService} from '@ngx-translate/core';
import {ConfirmationService, MessageService} from 'primeng/api';
import {ButtonModule} from 'primeng/button';
import {DialogModule} from 'primeng/dialog';
import {DynamicDialogConfig, DynamicDialogRef} from 'primeng/dynamicdialog';
import {TableModule} from 'primeng/table';
import {TabViewModule} from 'primeng/tabview';
import {TagModule} from 'primeng/tag';
import {Tam4SelectorConfig, TamAbstractReduxComponent} from '../../../components';
import {TamFullHeightPanelComponent} from '../../../components/tam-full-height-panel.component';
import {EventUtils} from '../../../utils';
import {DomainValuesOperationTypeTransformPipe} from '../common/status-transform.pipe';
import {ManagementValuesService} from '../management-values.service';
import {
    ConstraintData,
    DomainValueChangesRequestItem,
    DomainValueItemValidateError,
    DomainValuesChangeType,
    DomainValuesOperationType
} from '../models/management-values.types';
import {Ripple} from 'primeng/ripple';
import { isConstraintEdited} from '../common/domain-values-management.function';
import { ManagementValuesSelectors } from '../store/management-values.selectors';
import {ComponentsModule} from '../../components/components.module';

const storeSelectors: Tam4SelectorConfig[] = [
  { key: 'loading', selector: ManagementValuesSelectors.getDomainValuesLoading },
  { key: 'error', selector: ManagementValuesSelectors.getSavingError }
];

@Component({
    selector: 'div[managementValuesSummaryChangesDialog]',
    template: `
        <div tamFullHeightPanel class="modal" contentClass="flex flex-column">
            <tam-progress-spinner [active]="selMap?.loading | async"></tam-progress-spinner>
            <ng-template pTemplate="header">
                <h5 class="modal-title p-3">
                    <span class="font-semibold">{{ "domainValuesManagement.summaryChanges.title" | translate }}</span>
                </h5>
            </ng-template>
            <div class="m-3">
                <h5 class="text-primary">
                    <span class="font-semibold">{{ "domainValuesManagement.summaryChanges.description" | translate }}</span>
                </h5>
            </div>
            <p-tabView [activeIndex]="activeTabIndex()" (activeIndexChange)="activeTabIndex.set($event)">
                <p-tabPanel header="{{ 'domainValuesManagement.summaryChanges.tabs.details' | translate }}">
                    <p-table [value]="domainChangesValue"
                             [expandedRowKeys]="expandedRowKeys"
                             dataKey="changeId"
                             sortField="changeId"
                             sortMode="single"
                             scrollHeight="flex"
                             [scrollable]="true"
                             [tableStyle]="{ width: '100%', height: '100%' }"
                             styleClass="w-100 p-datatable-compat">
                        <ng-template pTemplate="header">
                            <tr>
                                <th></th>
                                <th>{{ "domainValuesManagement.summaryChanges.change" | translate }}</th>
                                <th>{{ "domainValuesManagement.summaryChanges.attribute" | translate }}</th>
                                <th>{{ "domainValuesManagement.summaryChanges.type" | translate }}</th>
                                <th>{{ "domainValuesManagement.summaryChanges.operationType" | translate }}</th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-row let-expanded="expanded">
                            <tr>
                                <td class="w-5rem">
                                    <button [pRowToggler]="row"
                                            class="p-button-text p-button-rounded p-button-plain"
                                            type="button" pButton pRipple
                                            [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
                                            [disabled]="!row.itemChanges || (row.itemChanges?.length === 0)">
                                    </button>
                                </td>
                                <td>{{ row.changeId }}</td>
                                <td><span class="font-semibold">{{ row.attributeId }}</span></td>
                                <td>{{ row.changeType }}</td>
                                <td>
                                    <p-tag [value]="row.operationType ?? row.itemChanges[0].operationType"
                                           [severity]="(row.operationType ?? row.itemChanges[0].operationType) | domainValuesOperationTypeTransform"
                                           styleClass="ml-2">
                                    </p-tag>
                                </td>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="rowexpansion" let-row>
                            <tr>
                                <td colspan="5" class="row-expand">
                                    <div class="changes-content" style="max-height: 400px; overflow-y: auto;">
                                        @for (change of row.itemChanges; track change.id) {
                                            @if (change.changeType === tabType.VALUES) {
                                                <div class="change-item">
                                                    <div class="change-header">
                                                        <strong>{{ "domainValuesManagement.summaryChanges.values.valueId" | translate }}</strong> {{ change.id }}
                                                        <p-tag [value]="change.operationType"
                                                               [severity]="change.operationType | domainValuesOperationTypeTransform"
                                                               styleClass="ml-2">
                                                        </p-tag>
                                                    </div>
                                                    @if (change.operationType === operationTypeStatus.UPDATE || change.operationType === operationTypeStatus.ADD) {
                                                        @for (locale of change.locales; track locale) {
                                                            @if (locale.newValue !== null && locale.newValue !== "") {
                                                                <div class="locale-change">
                                                                    <strong>{{ locale.language }}:</strong>
                                                                    @if (locale.oldValue !== null && locale.oldValue !== "") {
                                                                        <span class="old-value">{{ locale.oldValue }}</span> → <span
                                                                                class="new-value">{{ locale.newValue }}</span>
                                                                    } @else {
                                                                        <span class="new-value">{{ locale.newValue }}</span>
                                                                    }
                                                                </div>
                                                            }
                                                        }
                                                    }
                                                </div>
                                            } @else {
                                                @if (hasConstraintEdit(change)) {
                                                    <div class="change-item">
                                                        <div class="constraint-change">
                                                            <div class="constraint-header">
                                                                <strong>{{ "domainValuesManagement.summaryChanges.values.valueId" | translate }} </strong> {{ change.testAttributeValue }}
                                                                <p-tag [value]="change.operationType"
                                                                       [severity]="change.operationType | domainValuesOperationTypeTransform"
                                                                       styleClass="ml-2">
                                                                </p-tag>
                                                            </div>
                                                            <div class="constraint-values">
                                                                @if (change.addedValidValues?.length > 0) {
                                                                    <div class="added-value">
                                                                        <strong style="color:black;">
                                                                            {{ "domainValuesManagement.summaryChanges.constraint.addedValidValues" | translate }}</strong>
                                                                        @for (addValidValue of change.addedValidValues; let last = $last; track addValidValue) {
                                                                            <span>{{ addValidValue }}@if (!last) {
                                                                                |
                                                                            }</span>
                                                                        }
                                                                    </div>
                                                                }
                                                                @if (change.removedValidValues?.length > 0) {
                                                                    <div class="removed-value">
                                                                        <strong style="color:black; text-decoration: none !important;">{{ "domainValuesManagement.summaryChanges.constraint.removedValidValues" | translate }}</strong>
                                                                        @for (removedValidValue of change.removedValidValues; let last = $last; track removedValidValue) {
                                                                            <span>{{ removedValidValue }}
                                                                                @if (!last) {
                                                                                    |
                                                                                }
                                                                            </span>
                                                                        }
                                                                    </div>
                                                                }
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                            }
                                        }
                                    </div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </p-tabPanel>
                <p-tabPanel header="{{ 'domainValuesManagement.summaryChanges.tabs.errors' | translate }}" [disabled]="!hasValidationErrors()">
                    <p-table [value]="validationErrors()"
                             scrollHeight="flex"
                             [scrollable]="true"
                             [tableStyle]="{ width: '100%', height: '100%' }"
                             styleClass="w-100 p-datatable-compat">
                        <ng-template pTemplate="header">
                            <tr>
                                <th>{{ "domainValuesManagement.summaryChanges.errors.attributeId" | translate }}</th>
                                <th>{{ "domainValuesManagement.summaryChanges.errors.valueId" | translate }}</th>
                                <th>{{ "domainValuesManagement.summaryChanges.errors.locale" | translate }}</th>
                                <th>{{ "domainValuesManagement.summaryChanges.errors.localizedValue" | translate }}</th>
                                <th>{{ "domainValuesManagement.summaryChanges.errors.errorCode" | translate }}</th>
                                <th>{{ "domainValuesManagement.summaryChanges.errors.errorDescription" | translate }}</th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-error>
                            <tr>
                                <td><span class="font-semibold">{{ error.attributeId }}</span></td>
                                <td>{{ error.valueId }}</td>
                                <td>{{ error.locale }}</td>
                                <td>{{ error.localizedValue }}</td>
                                <td><span class="font-semibold text-red-500">{{ error.errorCode }}</span></td>
                                <td>{{ error.description }}</td>
                            </tr>
                        </ng-template>
                    </p-table>
                </p-tabPanel>
            </p-tabView>
            <ng-template pTemplate="footer">
                <div class="flex justify-content-end gap-2">
                    <p-button
                            severity="secondary"
                            styleClass="p-button-fixed-size"
                            icon="fas fa-times"
                            [label]="'domainValuesManagement.button.buttonCancel' | translate"
                            (onClick)="doCancel($event)">
                    </p-button>
                    <p-button
                            styleClass="p-button-fixed-size"
                            icon="fas fa-check"
                            [label]="'domainValuesManagement.button.buttonSubmit' | translate"
                            (onClick)="doConfirm($event)">
                    </p-button>
                </div>
            </ng-template>
        </div>
    `,
  standalone: true,
    imports: [
        TableModule,
        HttpClientModule,
        TagModule,
        TabViewModule,
        CommonModule,
        ButtonModule,
        TranslateModule,
        DomainValuesOperationTypeTransformPipe,
        DialogModule,
        TamFullHeightPanelComponent,
        Ripple,
        ComponentsModule
    ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./management-values-summary-changes.dialog.scss']
})

export class ManagementValuesSummaryChangesDialog extends TamAbstractReduxComponent<SelectorMap> {


    constructor(protected translate: TranslateService,
                protected tamTranslate: Tam4TranslationService,
                protected store: Store,
                private service: ManagementValuesService,
                private ref: DynamicDialogRef,
                private config: DynamicDialogConfig,
                private confirmationService: ConfirmationService,
                private messageService: MessageService) {
        super(translate, tamTranslate, store, storeSelectors);

    }

    domainChangesValue: DomainValueChangesRequestItem[];
    descriptionFilter: string;
    codeFilter: string;
    showOnlyOpen = false;
    index = 0;
    expandedRowKeys: { [key: string]: boolean } = {};
    isSaving = false;

    activeTabIndex = signal(0);
    validationErrors = signal<DomainValueItemValidateError[]>([]);
    hasValidationErrors = signal(false);

    protected readonly tabType = DomainValuesChangeType;
    protected readonly operationTypeStatus = DomainValuesOperationType;

    private readonly domainChangesEffect = effect(() => {
        const loading = this.signals?.loading?.();
        const error = this.signals?.error?.();
        if (loading === false && !error) {
            if (this.isSaving) {
                this.ref.close();
                this.isSaving = false;
            }
        }
    }, { allowSignalWrites: true });

    ngOnInit() {
      super.ngOnInit();
      this.domainChangesValue = this.config.data;
    }

    ngOnDestroy(): void {
        super.ngOnDestroy();
    }

    doConfirm($event: any) {
        EventUtils.stopPropagation($event);

        this.confirmationService.confirm({
          target: null,
          key: 'mainConfirmDialog',
          message: this.translate.instant('domainValuesManagement.summaryChanges.message'),
          icon: 'pi pi-exclamation-triangle',
          acceptIcon: 'fa-solid fa-check',
          rejectIcon: 'fa-solid fa-x',
          acceptLabel: this.translate.instant('domainValuesManagement.summaryChanges.confirm'),
          rejectLabel: this.translate.instant('domainValuesManagement.button.buttonCancel'),
          rejectButtonStyleClass: 'p-button-text',
          accept: () => {
              this.validateAndSaveChanges();
          }
        });
    }

    doCancel($event: any) {
      EventUtils.stopPropagation($event);
      this.ref.close();
    }

    private validateAndSaveChanges(): void {
        this.service.action_doValidateChanges(
            this.domainChangesValue,
            (result: DomainValueItemValidateError[]) => this.handleValidationResult(result),
            (error: DomainValueItemValidateError[]) => this.handleValidationError(error)
        );
    }

    private handleValidationResult(isValid: DomainValueItemValidateError[]): void {
        if (!isValid || isValid.length === 0) {
            this.isSaving = true;
            this.service.action_doSaveChanges(this.domainChangesValue);
        } else {
            this.showError('domainValuesManagement.summaryChanges.validationFailed');
        }
    }

    private handleValidationError(response: DomainValueItemValidateError[] | any): void {
        if (Array.isArray(response) && response.length > 0) {
            this.validationErrors.set(response);
            this.hasValidationErrors.set(true);
            this.activeTabIndex.set(1);
            this.showError('domainValuesManagement.summaryChanges.validationErrorsFound');
        } else {
            this.showError('domainValuesManagement.summaryChanges.validationError');
        }
    }

    private showError(messageKey: string): void {
        this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('domainValuesManagement.messages.error'),
            detail: this.translate.instant(messageKey)
        });
    }

    protected hasConstraintEdit(constraint: ConstraintData) {
        return isConstraintEdited([constraint]);
    }
}
