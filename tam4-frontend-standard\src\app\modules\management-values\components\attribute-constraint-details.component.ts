import {Component, computed, effect, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, signal} from '@angular/core';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { SelectorMap } from '@creactives/models';
import { Tam4TranslationService } from '@creactives/tam4-translation-core';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from "primeng/button";
import { CardModule } from "primeng/card";
import { DividerModule } from "primeng/divider";
import { MultiSelectModule } from 'primeng/multiselect';
import { TableModule } from "primeng/table";
import { TooltipModule } from 'primeng/tooltip';
import { Tam4SelectorConfig, TamAbstractReduxComponent } from 'src/app/components';
import { ManagementValuesSelectors } from '../store/management-values.selectors';
import { ManagementValuesService } from '../management-values.service';
import {
  ConstraintsAttributeUpdate,
  ConstraintData,
  DomainValuesOperationType
} from '../models/management-values.types';
import { DomainValuesOperationTypeTransformPipe } from '../common/status-transform.pipe';
import { TagModule } from 'primeng/tag';
import {CommonModule} from '@angular/common';

const storeSelectors: Tam4SelectorConfig[] = [
  {key: 'constraints', selector: ManagementValuesSelectors.getConstraintsByAttribute},
  {key: 'selectedAttributeNode', selector: ManagementValuesSelectors.getSelectedAttributeNode},
];

@Component({
  selector: 'div[constraintDetails]',
  template: `
    <form [formGroup]="formSignal()">
      <p-table
          [value]="constraints()"
          [loading]="loading()"
          styleClass="w-100 p-datatable-compat">
        <ng-template pTemplate="header">
          <tr>
            <th>{{ 'domainValuesManagement.constraintDetails.attributeConstrained' | translate }}</th>
            <th>{{ 'domainValuesManagement.constraintDetails.valuesConstrained' | translate }}</th>
            <th>{{ 'domainValuesManagement.constraintDetails.attributeValues' | translate }}</th>
            @if (isEditable()) {
              <th class="text-center">{{ 'domainValuesManagement.constraintDetails.status' | translate }}</th>
              <th class="text-center">{{ 'domainValuesManagement.constraintDetails.actions' | translate }}</th>
            }
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-constraint>
          <tr [class]="getRowClasses(constraint)">
            <td>{{ constraint.testAttributeId }}</td>
            <td>
              @if (constraint && constraint.constraintOptions) {
                <p-multiSelect class="p-inputgroup w-full"
                               [options]="getEnhancedConstraintOptions(constraint.refId)"
                               [formControl]="getFormControl(constraint.refId + '_val')"
                               optionLabel="text"
                               optionValue="key"
                               display="chip"
                               appendTo="body"/>
              }
            </td>
            <td>
              @if (constraint && signals?.constraints()?.attributeOptions) {
                <p-multiSelect class="p-inputgroup w-full"
                               [options]="getEnhancedOptions(constraint.refId)"
                               [formControl]="getFormControl(constraint.refId)"
                               optionLabel="text"
                               optionValue="key"
                               (onChange)="onSelectionChange($event, constraint.refId)"
                               appendTo="body">
                </p-multiSelect>
              }
            </td>
            @if (isEditable()) {
              <td class="text-center">
                @if (constraint.operationType) {
                  <p-tag [value]="constraint.operationType"
                         [severity]="constraint.operationType | domainValuesOperationTypeTransform">
                  </p-tag>
                }
              </td>
              <td class="text-center">
                @if (!isConstraintDeleted(constraint)) {
                  <button type="button"
                          (click)="onConfirmDeleteConstraint(constraint)"
                          class="p-button p-button-text p-0 border-0 bg-transparent action-button delete-button"
                          pTooltip="{{ 'domainValuesManagement.constraintDetails.deleteConstraint' | translate }}"
                          tooltipPosition="top">
                    <i class="pi pi-trash"></i>
                  </button>
                } @else {
                  <button type="button"
                          (click)="onRestoreConstraint(constraint)"
                          class="p-button p-button-text p-0 border-0 bg-transparent action-button restore-button"
                          pTooltip="{{ 'domainValuesManagement.constraintDetails.restoreConstraint' | translate }}"
                          tooltipPosition="top">
                    <i class="pi pi-undo"></i>
                  </button>
                }
              </td>
            }
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
          <tr>
            <td [attr.colspan]="isEditable() ? 5 : 4" class="text-center p-4">
              <div class="p-3 border-round bg-gray-100">
                <i class="pi pi-info-circle text-xl mb-2 text-primary"></i>
                <p>{{ 'domainValuesManagement.constraintDetails.noConstraintsMessage' | translate }}</p>
              </div>
            </td>
          </tr>
        </ng-template>
      </p-table>
    </form>
  `,
  styles: [``],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DividerModule,
    TranslateModule,
    CardModule,
    ButtonModule,
    TableModule,
    MultiSelectModule,
    TooltipModule,
    DomainValuesOperationTypeTransformPipe,
    TagModule
  ],
  host: {
    '[class]': '\'constraint-details-component\''
  }
})
export class AttributeConstraintDetailsComponent extends TamAbstractReduxComponent<SelectorMap> implements OnInit, OnDestroy {

  constructor() {
    super(
        inject(TranslateService),
        inject(Tam4TranslationService),
        inject(Store),
        storeSelectors
    );
  }
  protected fb = inject(FormBuilder);
  protected translate = inject(TranslateService);
  protected tamTranslate = inject(Tam4TranslationService);
  protected service = inject(ManagementValuesService);
  protected confirmationService = inject(ConfirmationService);

  loading = signal(false);
  elementRemoved = signal<string[]>([]);
  elementAdded = signal<string[]>([]);

  constraints = computed(() => {
    const constraints = this.signals?.constraints()?.constraints;
    return constraints?.length ? constraints : [];
  });

  attribute = computed(() => this.signals?.selectedAttributeNode());
  isEditable = computed(() => this.attribute()?.editable ?? false);
  formSignal = signal<FormGroup>(this.fb.group({}));
  form = computed(() => this.formSignal());

  private formUpdateEffect = effect(() => {
    const constraints = this.constraints();
    if (!constraints?.length) {
      this.formSignal.set(this.fb.group({}));
      return;
    }

    const group: { [key: string]: FormControl } = {};
    const isEditable = this.isEditable();

    constraints.forEach((constraint) => {
      const control = new FormControl(constraint.validValues);

      if (!isEditable || this.isConstraintDeleted(constraint)) {
        control.disable();
      } else {
        control.enable();
      }

      group[constraint.refId] = control;
      group[constraint.refId + '_val'] = new FormControl({
        value: constraint.testAttributeValues,
        disabled: true
      });
    });

    this.formSignal.set(this.fb.group(group));
  }, { allowSignalWrites: true });

  enhancedOptionsMap = computed(() => {
    const constraints = this.constraints();
    const currentForm = this.formSignal();
    const baseOptions = this.signals?.constraints()?.attributeOptions || [];
    return this.buildEnhancedOptionsMap(
      constraints,
      currentForm,
      () => baseOptions,
      (constraint) => constraint.refId
    );
  });

  enhancedConstraintOptionsMap = computed(() => {
    const constraints = this.constraints();
    const currentForm = this.formSignal();
    return this.buildEnhancedOptionsMap(
      constraints,
      currentForm,
      (constraint) => constraint.constraintOptions || [],
      (constraint) => constraint.refId + '_val'
    );
  });

 private buildEnhancedOptionsMap(
    constraints: any[],
    currentForm: FormGroup,
    getBaseOptions: (constraint: any) => any[],
    getFormKey: (constraint: any) => string
  ): Map<string, any[]> {
    const enhancedMap = new Map<string, any[]>();

    constraints.forEach(constraint => {
      const baseOptions = getBaseOptions(constraint);
      const optionsMap = new Map();
      baseOptions.forEach(option => optionsMap.set(option.key, option));

      const selectedValues = currentForm.get(getFormKey(constraint))?.value || [];
      const missingOptions = selectedValues
        .filter(value => value && !optionsMap.has(value))
        .map(value => ({
          key: value,
          text: value
        }));

      enhancedMap.set(getFormKey(constraint), [...baseOptions, ...missingOptions]);
    });

    return enhancedMap;
  }

  // getConstraints() {
  //   return this.constraints();
  // }

  getFormControl(name: string): FormControl {
    const control = this.formSignal().get(name); // Usa il signal writable
    if (!control) {
      return this.fb.control({ value: null, disabled: true });
    }
    return control as FormControl;
  }

  getEnhancedOptions(refId: string) {
    return this.enhancedOptionsMap().get(refId) || [];
  }

  getEnhancedConstraintOptions(refId: string) {
    return this.enhancedConstraintOptionsMap().get(refId + '_val') || [];
  }

  onSelectionChange(event: any, refId: string) {
    const constraint = this.constraints().find(el => el.refId === refId);
    if (!constraint) { return; }

    const oldValues = constraint.oldValidValues || [];
    const newValues = event.value || [];

    const removed = oldValues.filter(elem => !newValues.includes(elem));
    const added = newValues.filter(elem => !oldValues.includes(elem));

    this.elementRemoved.set(removed);
    this.elementAdded.set(added);

    const data: ConstraintsAttributeUpdate = {
      attributeId: this.attribute()?.key,
      refId,
      validValues: newValues,
      addedValidValues: added,
      removedValidValues: removed,
      edited: true,
      operationType: DomainValuesOperationType.UPDATE
    };

    this.service.action_doUpdateConstraint(data);
  }

  isConstraintDeleted = (constraint: ConstraintData): boolean =>
      constraint.operationType === DomainValuesOperationType.DELETE

  getRowClasses = (rowData: ConstraintData): Record<string, boolean> => ({
    'row-deleted': this.isConstraintDeleted(rowData)
  })

  async onConfirmDeleteConstraint(constraint: ConstraintData): Promise<void> {
    const confirmed = await this.showConfirmDialog(
        'domainValuesManagement.constraintDetails.deleteMessage',
        'domainValuesManagement.constraintDetails.deleteConfirmTitle',
        'p-button-danger'
    );

    if (confirmed) {
      this.onDeleteConstraint(constraint);
    }
  }

  async onRestoreConstraint(constraint: ConstraintData): Promise<void> {
    const confirmed = await this.showConfirmDialog(
        'domainValuesManagement.constraintDetails.restoreMessage',
        'domainValuesManagement.constraintDetails.restoreConfirmTitle',
        'p-button-outlined p-button-sm'
    );

    if (confirmed) {
      this.doRestoreConstraint(constraint);
    }
  }

  private showConfirmDialog(
      message: string,
      header: string,
      acceptButtonStyle: string
  ): Promise<boolean> {
    return new Promise((resolve) => {
      this.confirmationService.confirm({
        key: 'mainConfirmDialog',
        message: this.translate.instant(message),
        header: this.translate.instant(header),
        icon: 'pi pi-exclamation-triangle',
        acceptIcon: 'fa-solid fa-check',
        rejectIcon: 'fa-solid fa-x',
        acceptLabel: this.translate.instant('domainValuesManagement.summaryChanges.confirm'),
        rejectLabel: this.translate.instant('domainValuesManagement.button.buttonCancel'),
        rejectButtonStyleClass: 'p-button-text',
        acceptButtonStyleClass: acceptButtonStyle,
        accept: () => resolve(true),
        reject: () => resolve(false)
      });
    });
  }

  private onDeleteConstraint(constraint: ConstraintData): void {
    const attribute = this.attribute();
    if (!attribute) { return; }

    constraint.previousOperationType = constraint.operationType;
    constraint.operationType = DomainValuesOperationType.DELETE;

    const constraintUpdate: ConstraintsAttributeUpdate = {
      attributeId: attribute.key,
      refId: constraint.refId,
      validValues: constraint.validValues,
      addedValidValues: constraint.addedValidValues || [],
      removedValidValues: constraint.removedValidValues || [],
      previousOperationType: constraint.previousOperationType,
      operationType: constraint.operationType,
      edited: true
    };

    this.service.action_doUpdateConstraint(constraintUpdate);
  }

  private doRestoreConstraint(constraint: ConstraintData): void {
    const attribute = this.attribute();
    if (!attribute) { return; }

    constraint.operationType = constraint.previousOperationType || null;
    constraint.previousOperationType = constraint.operationType;

    const constraintUpdate: ConstraintsAttributeUpdate = {
      attributeId: attribute.key,
      refId: constraint.refId,
      validValues: constraint.validValues,
      addedValidValues: constraint.addedValidValues || [],
      removedValidValues: constraint.removedValidValues || [],
      operationType: constraint.operationType,
      previousOperationType: null,
      edited: constraint.operationType != null
    };

    this.service.action_doUpdateConstraint(constraintUpdate);
  }

  ngOnDestroy() {
    super.ngOnDestroy();
  }

}
