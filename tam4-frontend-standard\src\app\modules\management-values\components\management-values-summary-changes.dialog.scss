.change-item {
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background-color: var(--surface-50);
  border-radius: 4px;
  border-left: 3px solid var(--primary-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    background-color: var(--surface-100);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }
}

.change-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;

  strong {
    color: var(--text-color);
  }
}

// Locale changes
.locale-change {
  margin: 0.25rem 0;
  padding: 0.25rem 0.5rem;
  background-color: var(--surface-0);
  border-radius: 3px;
  font-size: 0.8125rem;

  strong {
    color: var(--primary-color);
    margin-right: 0.25rem;
  }
}

.removed-value {
  color: var(--red-500);
  margin-right: 0.25rem;
}

.removed-value span {
  text-decoration: line-through;
}

.removed-value strong {
  color: var(--black-500);
  text-decoration: none;
}

.added-value {
  color: var(--green-600);
  font-weight: 500;
}

.constraint-change {
  margin: 0.5rem 0;
}

.constraint-header {
  margin-bottom: 0.25rem;
  font-size: 0.875rem;

  strong {
    color: var(--text-color);
  }
}

.constraint-values {
  padding-left: 1rem;
  font-size: 0.8125rem;
}
